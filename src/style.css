* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: Arial, Helvetica, sans-serif;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.geoportal-header {
  background: linear-gradient(90deg, #9f3030, #84a5ff);
  color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  gap: 20px;
}

.header-logo {
  flex-shrink: 0;
}

.institution-logo {
  height: 60px;
  width: auto;
  object-fit: contain;
  padding: 5px;
  border-radius: 4px;
}

.header-title {
  flex: 1;
  text-align: center;
  min-width: 0;
}

.header-title h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.header-title p {
  margin: 0;
  font-size: 14px;
  color: #adb5bd;
  font-weight: normal;
}

.geoportal-container {
  display: flex;
  width: 100%;
  flex: 1;
  position: relative;
  min-height: 0;
}

#map-container {
  flex: 1;
  position: relative;
  height: 100%;
}

#map {
  width: 100%;
  height: 100%;
}

.sidebar {
  position: absolute;
  top: 10px;
  left: 90px;
  width: 320px; /* Slightly wider to accommodate wrapped text */
  height: fit-content;
  background-color: #0f5397;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: visible;
  /* Remove transition for smoother dragging */
  will-change: transform; /* Hint for hardware acceleration */
}

.sidebar-header {
  padding: 15px;
  background-color: #343a40;
  color: white;
  border-bottom: 1px solid #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab; /* Better cursor for draggable elements */
  user-select: none; /* Prevent text selection during drag */
}

.sidebar-content {
  padding: 10px;
  max-height: 70vh;
  overflow-y: auto;
  font-size: 12px; /* Ensure consistent font size throughout the sidebar */
}

.hidden {
  display: none;
}

/* Draggable elements */
.draggable-header {
  position: relative;
  padding-left: 25px !important; /* Make room for the drag handle */
  cursor: grab; /* Better cursor for draggable elements */
  user-select: none; /* Prevent text selection during drag */
}

.draggable-header::before {
  content: '⋮⋮';
  position: absolute;
  left: 8px;
  opacity: 0.5;
  font-size: 12px;
  pointer-events: none;
}

.draggable-header h2 {
  margin: 0;
  font-size: 16px;
}

.draggable-header.dragging {
  cursor: grabbing !important;
}

/* Ensure panels can be dragged anywhere */
.sidebar, .legend-panel, .search-panel {
  position: absolute;
  z-index: 1500; /* Ensure panels are above other elements */
  will-change: transform; /* Hint to browser to use hardware acceleration */
  transition: box-shadow 0.2s ease; /* Smooth transition for shadow effect */
}

/* Style for panels while being dragged */
.dragging {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); /* Enhanced shadow while dragging */
  cursor: grabbing !important;
  opacity: 0.95; /* Slight transparency while dragging */
}

/* Floating Action Menu */
.floating-menu {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.menu-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: #f8f9fa;
  transform: scale(1.05);
}

.menu-button:active {
  transform: scale(0.95);
}

.close-button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
}

.layer-item {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  display: flex;
  flex-wrap: nowrap; /* Prevent wrapping of the flex items */
  align-items: flex-start; /* Align with the top of wrapped text */
}

.layer-item label {
  margin-left: 8px;
  flex: 1;
  min-width: 0; /* Allow label to shrink below its content size */
  cursor: pointer;
  font-size: 12px; /* Smaller font size */
  line-height: 1.3; /* Better line height for readability */
  word-wrap: break-word; /* Ensure text wraps */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
  white-space: normal; /* Allow text to wrap */
  display: inline-block; /* Better text wrapping */
}

.layer-item input[type="checkbox"] {
  cursor: pointer;
  margin-top: 2px; /* Align checkbox with the first line of text */
  flex-shrink: 0; /* Prevent checkbox from shrinking */
}

/* Layer info icon */
.layer-info-icon {
  margin-left: 5px;
  color: #6c757d;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px; /* Align with first line of text */
  min-width: 16px; /* Ensure consistent width */
  width: 16px; /* Fixed width */
  height: 16px;
  position: relative; /* For proper positioning */
  z-index: 10; /* Ensure it's above other elements */
  flex-shrink: 0; /* Prevent icon from shrinking */
  align-self: flex-start; /* Align to the top */
}

.layer-info-icon:hover {
  color: #007bff;
}

/* Layer tooltip */
.layer-tooltip {
  position: fixed; /* Fixed position relative to viewport */
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 2000; /* Higher than other elements */
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: break-word;
  pointer-events: none; /* Allow mouse events to pass through */
  opacity: 0.95; /* Slight transparency */
  transition: opacity 0.2s ease; /* Smooth transition */
  max-height: 200px; /* Limit height */
  overflow-y: auto; /* Add scrollbar if needed */
}

/* Layer Group Styles */
.layer-group {
  margin-bottom: 10px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.layer-group-header {
  padding: 8px;
  display: flex;
  flex-wrap: nowrap; /* Prevent wrapping of flex items */
  align-items: flex-start; /* Align with the top of wrapped text */
  background-color: #e9ecef;
  cursor: pointer;
  user-select: none;
}

.layer-group-header:hover {
  background-color: #dee2e6;
}

.layer-group-toggle {
  margin-right: 5px;
  min-width: 16px; /* Use min-width instead of width */
  width: 16px; /* Fixed width */
  height: 16px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-top: 2px; /* Align with first line of text */
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.layer-group-toggle.expanded {
  color: #495057;
}

.layer-group-toggle.collapsed {
  color: #6c757d;
}

.layer-group-header input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
  margin-top: 2px; /* Align checkbox with the first line of text */
  flex-shrink: 0; /* Prevent checkbox from shrinking */
}

.layer-group-header label {
  flex: 1;
  min-width: 0; /* Allow label to shrink below its content size */
  cursor: pointer;
  font-size: 12px; /* Smaller font size */
  font-weight: bold; /* Make group headers bold to distinguish them */
  line-height: 1.3; /* Better line height for readability */
  word-wrap: break-word; /* Ensure text wraps */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
  white-space: normal; /* Allow text to wrap */
  display: inline-block; /* Better text wrapping */
}

.layer-group-children {
  padding: 0 0 5px 0;
  width: 100%; /* Ensure full width */
}

.layer-group-children .layer-item {
  margin: 5px 10px;
  border-radius: 3px;
  padding: 6px; /* Slightly smaller padding for nested items */
  width: calc(100% - 20px); /* Account for margins */
  box-sizing: border-box; /* Include padding in width calculation */
}

.legend-panel {
  position: absolute;
  top: 90px;
  left: 80%;
  width: 250px;
  height: fit-content;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  overflow-y: visible;
  /* Remove transition for smoother dragging */
  will-change: transform; /* Hint for hardware acceleration */
}

.legend-header {
  padding: 15px;
  background-color: #343a40;
  color: white;
  border-bottom: 1px solid #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab; /* Better cursor for draggable elements */
  user-select: none; /* Prevent text selection during drag */
}

.legend-content {
  padding: 10px;
  max-height: 70vh;
  overflow-y: auto;
  font-size: 12px; /* Match layer panel text size */
}

/* Search Panel */
.search-panel {
  position: absolute;
  top: 10px;
  left: calc(50% - 250px); /* Center without using transform */
  width: 80%;
  max-width: 500px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  /* Remove transition for smoother dragging */
  will-change: transform; /* Hint for hardware acceleration */
}

.search-header {
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid #dee2e6;
  cursor: grab; /* Better cursor for draggable elements */
  background-color: #343a40;
  color: white;
  user-select: none; /* Prevent text selection during drag */
}

#search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.search-result-item {
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.search-result-item:hover {
  background-color: #f1f1f1;
}

.search-result-item:last-child {
  border-bottom: none;
}

.legend-item {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.legend-item h3 {
  font-size: 12px; /* Match layer panel text size */
  margin-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 5px;
  word-wrap: break-word; /* Ensure text wraps */
  overflow-wrap: break-word; /* Modern browsers */
  hyphens: auto; /* Add hyphens for very long words */
  line-height: 1.3; /* Better line height for readability */
  white-space: normal; /* Allow text to wrap */
}

.legend-symbol {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-symbol-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.legend-symbol-label {
  font-size: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  flex: 1;
  min-width: 0;
}

/* Legend image and controls container */
.legend-image-container {
  display: flex;
  flex-direction: row; /* Changed to row to place image and controls side by side */
  align-items: flex-start;
  margin-bottom: 10px;
  width: 100%;
  gap: 10px; /* Add space between image and controls */
}

.legend-image {
  max-width: 120px; /* Limit image width */
  max-height: 150px; /* Limit image height */
  object-fit: contain; /* Maintain aspect ratio */
  margin-bottom: 0;
}

/* Controls container */
.legend-controls {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0; /* Allow container to shrink */
}

/* Opacity slider container */
.opacity-slider-container {
  display: flex;
  align-items: center;
  margin-top: 5px;
  width: 100%;
}

.opacity-slider-icon {
  color: #6c757d;
  font-size: 14px;
  margin: 0 5px;
}

.opacity-slider-icon.faded {
  opacity: 0.3;
}

.opacity-slider {
  flex: 1;
  height: 5px;
  -webkit-appearance: none;
  appearance: none;
  background: #dee2e6;
  outline: none;
  border-radius: 3px;
  margin: 0 5px;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Opacity input container */
.opacity-input-container {
  display: flex;
  align-items: center;
  margin-top: 8px;
  width: 100%;
}

/* Opacity input field */
.opacity-input {
  width: 60px;
  height: 24px;
  padding: 2px 5px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin: 0 5px;
}

.opacity-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading and error messages */
.loading, .error, .no-layers {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 10px;
  text-align: center;
}

.loading {
  background-color: #e9f5ff;
  color: #0066cc;
  border: 1px solid #b3d7ff;
}

.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.no-layers {
  background-color: #fff9e6;
  color: #856404;
  border: 1px solid #ffeeba;
}

/* Custom positioning for Leaflet controls */
.leaflet-top.leaflet-left {
  margin-top: 200px; /* Adjust this value based on the height of your floating menu */
}

/* Footer Styles */
.geoportal-footer {
  background-color: #343a40;
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  position: relative;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-project-text {
  margin: 0 0 10px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #f8f9fa;
  font-weight: 500;
}

.footer-rights {
  margin: 0;
  font-size: 12px;
  color: #adb5bd;
}

.footer-link {
  color: #17a2b8;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #20c997;
  text-decoration: underline;
}

/* Point Modal Styles */
.point-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.point-modal.hidden {
  display: none;
}

.point-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.point-modal-header {
  background-color: #343a40;
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.point-modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.point-modal-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.point-modal-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.point-modal-body {
  padding: 20px;
}

.point-coordinates {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.coordinate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.coordinate-item:last-child {
  margin-bottom: 0;
}

.coordinate-label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.coordinate-value {
  font-family: 'Courier New', monospace;
  background-color: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 13px;
  color: #212529;
  min-width: 120px;
  text-align: right;
}

.properties-title {
  margin-bottom: 12px;
  font-size: 14px;
  color: #495057;
}

.property-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.property-item:last-child {
  margin-bottom: 0;
}

.property-label {
  font-weight: 500;
  color: #6c757d;
  font-size: 13px;
  margin-right: 10px;
  min-width: 80px;
  flex-shrink: 0;
}

.property-value {
  font-size: 13px;
  color: #212529;
  text-align: right;
  word-break: break-word;
  flex: 1;
}

/* Interactive point styles */
.leaflet-interactive {
  cursor: pointer !important;
}

.leaflet-marker-icon {
  transition: transform 0.2s ease;
}

.leaflet-marker-icon:hover {
  transform: scale(1.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .geoportal-container {
    flex-direction: column;
  }

  .sidebar, .legend-panel {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-title h1 {
    font-size: 20px;
  }

  .header-title p {
    font-size: 12px;
  }

  .institution-logo {
    height: 50px;
  }

  .footer-project-text {
    font-size: 12px;
  }

  .footer-rights {
    font-size: 11px;
  }

  .point-modal-content {
    width: 95%;
    margin: 10px;
  }

  .point-modal-body {
    padding: 15px;
  }

  .coordinate-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .coordinate-value {
    min-width: auto;
    width: 100%;
    text-align: left;
  }

  .property-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .property-label {
    min-width: auto;
  }

  .property-value {
    text-align: left;
  }
}
